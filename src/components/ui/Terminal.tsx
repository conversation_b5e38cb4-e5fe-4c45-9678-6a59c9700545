'use client';
import React, { useState, useEffect } from 'react';
import TypedLine from './TypedLine';
import CommandButton from './CommandButton';
import cursorSvg from '../media/cursor.svg';

interface TerminalProps {
  onCommand?: (command: string) => void;
}

export type Line = {
  type: 'text' | 'prompt' | 'command';
  content: string;
};

const Terminal: React.FC<TerminalProps> = ({ onCommand }) => {
  const [visibleLines, setVisibleLines] = useState<number>(0);

  const allLines: Line[] = [
    { type: 'text', content: "We're building the" },
    { type: 'text', content: "next-gen operating system" },
    { type: 'text', content: "for AI agents." },
    { type: 'prompt', content: "/dev/agents > ls" },
    { type: 'command', content: 'about' },
    { type: 'command', content: 'jobs' },
    { type: 'command', content: 'who' }
  ];

  useEffect(() => {
    const timer = setTimeout(() => {
      if (visibleLines < allLines.length) {
        setVisibleLines(prev => prev + 1);
      }
    }, visibleLines === 0 ? 500 : 800);

    return () => clearTimeout(timer);
  }, [visibleLines, allLines.length]);

  const handleCommand = (command: string) => {
    if (onCommand) {
      onCommand(command);
    }
  };

  return (
    <div className="terminal">
      {allLines.slice(0, visibleLines).map((line, index) => (
        <TypedLine
          key={index}
          line={line}
          onCommand={handleCommand}
          delay={0}
        />
      ))}

      <div id="prompt">
        <span className="cwd">/dev/agents &gt;</span>
        <div className="cursor-placeholder">▋</div>
      </div>

      <div className="circle-container">
        <div className="circle" id="circle2"></div>
      </div>
    </div>
  );
};

export default Terminal;
