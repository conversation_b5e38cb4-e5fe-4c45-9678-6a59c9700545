'use client';
import React from 'react';
import CommandButton from './CommandButton';
import TypeWriter from './TypeWriter';
import { Line } from './Terminal';

interface TypedLineProps {
  line: Line;
  onCommand: (command: string) => void;
  delay?: number;
}

const TypedLine: React.FC<TypedLineProps> = ({ line, onCommand, delay = 0 }) => {
  if (!line) return null;

  const renderContent = () => {
    switch (line.type) {
      case 'text':
        return <TypeWriter text={line.content} speed={30} />;

      case 'prompt':
        return (
          <p className="old-prompt">
            <TypeWriter text={line.content} speed={20} />
          </p>
        );

      case 'command':
        return (
          <CommandButton
            command={line.content}
            onClick={() => onCommand(line.content)}
          />
        );

      default:
        return <TypeWriter text={line.content} speed={30} />;
    }
  };

  return (
    <div
      className="typedLine"
      style={{
        opacity: 1,
        animationDelay: `${delay}ms`
      }}
    >
      {renderContent()}
    </div>
  );
};

export default TypedLine;
