// src/components/ui/CommandButton.tsx

'use client';
import React from 'react';

// Define the shape of the props this component expects
interface CommandButtonProps {
  command: string;
  onClick: (event: React.MouseEvent<HTMLButtonElement>) => void; // A function for button clicks
}

// Apply the props interface to the component
const CommandButton: React.FC<CommandButtonProps> = ({ command, onClick }) => {
  return (
    <button
      className="executable" // This class name is good
      onClick={onClick}
    >
      {command}
    </button>
  );
};

export default CommandButton;