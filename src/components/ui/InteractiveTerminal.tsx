// src/components/ui/InteractiveTerminal.tsx

'use client';

import React, { useState } from 'react';

// Define the shape of the props this component expects
interface InteractiveTerminalProps {
  onCommand: (command: string) => void;
}

// Apply the props interface.
// This component is now simple and reusable.
const InteractiveTerminal: React.FC<InteractiveTerminalProps> = ({ onCommand }) => {
  const [inputValue, setInputValue] = useState<string>('');

  // This function handles the form submission
  const handleFormSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault(); // Prevents the page from reloading
    if (inputValue.trim()) {
      onCommand(inputValue); // Send the command up to the parent (page.tsx)
      setInputValue('');    // Clear the input field
    }
  };

  return (
    // We use a form for better accessibility and event handling (e.g., Enter key)
    <form onSubmit={handleFormSubmit} className="flex items-center w-full mt-2">
      <span className="text-green-400">espirai/lab &gt;</span>
      <input
        type="text"
        value={inputValue}
        onChange={(e) => setInputValue(e.target.value)}
        className="flex-1 bg-transparent border-none text-white focus:outline-none ml-2"
        autoFocus // Automatically focus the input when it appears
        autoComplete="off" // Disable browser autocomplete
      />
    </form>
  );
};

export default InteractiveTerminal;


