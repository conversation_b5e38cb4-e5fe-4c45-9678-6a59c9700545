@tailwind base;
@tailwind components;
@tailwind utilities;


/* Your other custom styles, if any, go below these lines */
  
  #root {
    height: 100vh;
    position: relative;
  }
  
  .terminal {
    padding: 2em;
    height: 100vh;
    position: relative;
    z-index: 2;
    color: #000000;
    font-size: 28px;
    line-height: 1.5;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    overflow-y: auto;
    background: white;
  }
  
  .typedLine {
    margin-bottom: 0.5em;
    opacity: 0;
    animation: fadeIn 0.5s ease-in-out forwards;
  }
  
  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }
  
  .old-prompt {
    margin: 0.5em 0;
    color: #666666;
  }
  
  .old-prompt span {
    color: #666666;
  }
  
  .executable {
    background: none;
    border: none;
    color: #0066cc;
    font-family: inherit;
    font-size: inherit;
    cursor: pointer;
    padding: 4px 8px;
    margin: 0 4px;
    text-decoration: underline;
    transition: all 0.2s ease;
    border-radius: 4px;
  }
  
  .executable:hover {
    color: #004499;
    background-color: #e6f3ff;
    transform: scale(1.05);
  }
  
  .clickable-command {
    background: none;
    border: none;
    color: #0066cc;
    font-family: inherit;
    font-size: inherit;
    cursor: pointer;
    padding: 4px 8px;
    margin: 0 8px;
    text-decoration: underline;
    transition: all 0.2s ease;
    border-radius: 4px;
    display: inline-block;
  }
  
  .clickable-command:hover {
    color: #004499;
    background-color: #e6f3ff;
    transform: scale(1.05);
  }
  
  #prompt {
    display: flex;
    align-items: center;
    margin-top: 1em;
  }
  
  /* Remove any background animations for clean paper look */
  
  .cwd {
    color: #666666;
    margin-right: 0.5em;
  }
  
  .cursor {
    width: 33.9219px;
    height: 33.9218px;
    animation: blink 1s infinite;
    filter: invert(1);
  }
  
  .cursor-placeholder {
    color: #000000;
    animation: blink 1s infinite;
    font-size: 28px;
    margin-left: 8px;
  }
  
  @keyframes blink {
    0%, 50% {
      opacity: 1;
    }
    51%, 100% {
      opacity: 0;
    }
  }
  
  .terminal-content {
    min-height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
  }
  
  .terminal-line {
    margin-bottom: 0.3em;
    min-height: 1.6em;
    display: flex;
    align-items: center;
  }
  
  .terminal-input {
    background: transparent;
    border: none;
    outline: none;
    font-family: inherit;
    font-size: inherit;
    color: inherit;
    flex: 1;
    margin-left: 0.5em;
  }
  
  .terminal-input:focus {
    outline: none;
  }
  
  .input-line {
    background: rgba(240, 240, 240, 0.5);
    padding: 6px 8px;
    border-radius: 4px;
    margin-top: 8px;
  }
  