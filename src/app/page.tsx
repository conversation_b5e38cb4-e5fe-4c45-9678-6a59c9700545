// src/app/page.tsx

'use client';

import React, { useState, useEffect, useRef } from 'react';
import InteractiveTerminal from '@/components/ui/InteractiveTerminal';
import TypedLine from '@/components/ui/TypedLine';
import { Line } from '@/components/ui/Terminal';

// Define commands type
interface Commands {
  [key: string]: string[];
}

const commands: Commands = {
  about: [
    "Espirai is an AI native software, ML, and Data engineering lab focused on creating custom practical, real-world solutions for businesses.",
    "Unlike companies that just offer advice, Espirai AI builds and deploys actual systems."
  ],
  services: [
    "- Automation & Hyperautomation",
    "- Data Engineering & Custom AI", 
    "- Agentic AI System Development",
    "- In-house Data Curation & Annotation (Powered by Bokoo)"
  ],
  work: [
    "-> case-study-fintech.md", 
    "-> case-study-ecommerce.md"
  ],
  lab: [
    "-> articles/", 
    "-> news/"
  ],
  help: [
    "Available commands: about, services, work, lab, help, clear"
  ],
};

export default function HomePage() {
  const [history, setHistory] = useState<Line[]>([]);
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    setHistory([
      { type: 'text', content: '// System Announcement: Espirai acquires Bokoo...' },
      { type: 'text', content: '// We now manage the entire AI development lifecycle.' },
      { type: 'prompt', content: 'espirai/lab > ls' },
      { type: 'command', content: 'about services work lab start_project' } 
    ]);
  }, []);

  useEffect(() => {
    if (containerRef.current) {
      containerRef.current.scrollTop = containerRef.current.scrollHeight;
    }
  }, [history]);

  const handleCommand = (command: string) => {
    const cmd = command.toLowerCase().trim();
    const newHistory: Line[] = [
      ...history,
      { type: 'prompt', content: `espirai/lab > ${command}` }
    ];

    if (cmd === 'clear') {
      setHistory([]);
      return;
    }

    if (cmd === 'ls') {
      newHistory.push({
        type: 'command',
        content: 'about services work lab start_project'
      });
    } else if (commands[cmd]) {
      commands[cmd].forEach(line => {
        newHistory.push({ type: 'text', content: line });
      });
    } else {
      newHistory.push({
        type: 'text',
        content: `Command not found: ${cmd}. Type 'help' for assistance.`
      });
    }
    
    setHistory(newHistory);
  };

  return (
    <div 
      ref={containerRef} 
      className="terminal bg-black text-white w-full h-screen overflow-y-auto p-4 font-mono"
      onClick={() => containerRef.current?.querySelector('input')?.focus()}
    >
      {history.map((line, index) => (
        <TypedLine key={index} line={line} onCommand={handleCommand} />
      ))}
      <InteractiveTerminal onCommand={handleCommand} />
    </div>
  );
}
